run pyinstaller to repackage application:

python -m PyInstaller --noconfirm --onedir --windowed `
  --name "Tire<PERSON>titcher" `
  --add-data "../extract_frames.exe;." `
  --add-data "../stitch_tire_4k.exe;." `
  --add-data "../stitch_tire_8k.exe;." `
  --add-data "../libasprintf-0.dll;." `
  --add-data "../libatomic-1.dll;." `
  --add-data "../libcharset-1.dll;." `
  --add-data "../libgcc_s_dw2-1.dll;." `
  --add-data "../libgcc_s_seh-1.dll;." `
  --add-data "../libgmp-10.dll;." `
  --add-data "../libgmpxx-4.dll;." `
  --add-data "../libgomp-1.dll;." `
  --add-data "../libiconv-2.dll;." `
  --add-data "../libintl-8.dll;." `
  --add-data "../libisl-23.dll;." `
  --add-data "../libmpc-3.dll;." `
  --add-data "../libmpfr-6.dll;." `
  --add-data "../libopencv_calib3d455.dll;." `
  --add-data "../libopencv_core455.dll;." `
  --add-data "../libopencv_dnn455.dll;." `
  --add-data "../libopencv_features2d455.dll;." `
  --add-data "../libopencv_flann455.dll;." `
  --add-data "../libopencv_gapi455.dll;." `
  --add-data "../libopencv_highgui455.dll;." `
  --add-data "../libopencv_imgcodecs455.dll;." `
  --add-data "../libopencv_imgproc455.dll;." `
  --add-data "../libopencv_ml455.dll;." `
  --add-data "../libopencv_objdetect455.dll;." `
  --add-data "../libopencv_photo455.dll;." `
  --add-data "../libopencv_stitching455.dll;." `
  --add-data "../libopencv_video455.dll;." `
  --add-data "../libopencv_videoio455.dll;." `
  --add-data "../libquadmath-0.dll;." `
  --add-data "../libssp-0.dll;." `
  --add-data "../libstdc++-6.dll;." `
  --add-data "../libwinpthread-1.dll;." `
  --add-data "../libzstd.dll;." `
  --add-data "../opencv_videoio_ffmpeg455_64.dll;." `
  --add-data "../zlib1.dll;." `
  --add-data "ffmpeg_bin/ffmpeg.exe;ffmpeg_bin" `
  --add-data "ffmpeg_bin/ffplay.exe;ffmpeg_bin" `
  --add-data "ffmpeg_bin/ffprobe.exe;ffmpeg_bin" `
  --add-data "resources;resources" `
  --add-data "plans;plans" `
  --collect-all "cv2" `
  --collect-all "numpy" `
  --hidden-import "PIL" `
  --hidden-import "cv2" `
  --hidden-import "numpy" `
  --hidden-import "customtkinter" `
  --hidden-import "ttkthemes" `
  main.py