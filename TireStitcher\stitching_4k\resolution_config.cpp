#include "config.h"

// Implementation of resolution-specific parameter methods

void StitchConfig::applyResolutionParams() {
    // 4K build only supports 4K parameters
    currentParams = get4KParams();
    safePrint("Applied 4K resolution parameters", verbose, *this);

    // Override individual parameters if explicitly set
    if (templateMatchPrecision != 2) { // 2 is the default, so if different, user set it
        currentParams.templateMatchPrecision = templateMatchPrecision;
        safePrint("Overriding template match precision with user value: " +
                 std::to_string(templateMatchPrecision), verbose, *this);
    } else {
        // Update the main config with resolution-specific value
        templateMatchPrecision = currentParams.templateMatchPrecision;
    }

    if (jpegQuality != 98) { // 98 is the default, so if different, user set it
        currentParams.jpegQuality = jpegQuality;
        safePrint("Overriding JPEG quality with user value: " +
                 std::to_string(jpegQuality), verbose, *this);
    } else {
        // Update the main config with resolution-specific value
        jpegQuality = currentParams.jpegQuality;
    }
}

ResolutionParams StitchConfig::get4KParams() {
    ResolutionParams params;

    // Strip width and overlap parameters optimized for 4K (3840x2160)
    params.stripWidthMargin = 0.15;        // 15% margin for strip width
    params.overlapPercentage = 0.10;       // 10% of movement as overlap
    params.fallbackStripWidth = 20;        // Smaller fallback for 4K
    params.minOverlapPixels = 5;           // Minimum overlap for 4K images

    // Template matching parameters - medium precision for speed
    params.templateMatchPrecision = 1;     // Medium precision (faster)
    params.correlationThreshold = 0.75;    // Slightly lower threshold for 4K
    params.highCorrelationThreshold = 0.90; // Lower high correlation threshold

    // Movement detection parameters
    params.opticalFlowScale = 0.3;         // Larger scale for 4K (less downscaling)
    params.smallMovementThreshold = 3.0;   // Lower threshold for 4K
    params.defaultMovementBase = 50.0;     // Smaller base movement for 4K
    params.defaultMovementScale = 8.0;     // Smaller scale factor

    // Performance parameters optimized for 4K
    params.framesPerTile = 150;            // More frames per tile for 4K
    params.movingAverageSize = 3;          // Smaller moving average for responsiveness
    params.preloadBatchSize = 25;          // More preloading for 4K

    // Quality parameters
    params.jpegQuality = 95;               // Slightly lower quality for 4K
    params.useEnhancedBlending = false;    // Simpler blending for speed

    return params;
}


