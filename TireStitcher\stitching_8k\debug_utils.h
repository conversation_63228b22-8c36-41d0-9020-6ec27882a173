#ifndef DEBUG_UTILS_H
#define DEBUG_UTILS_H

#include <string>
#include <sstream>
#include <iomanip>
#include <opencv2/opencv.hpp>  // Required for cv::Mat
#include "config.h"

// Declare this function inline so it can be used in multiple files
inline void debugFloatingPoint(const std::string& label, double value) {
    std::stringstream ss;
    ss << std::fixed << std::setprecision(12); // Maximum precision for debugging
    ss << "DEBUG FLOAT [" << label << "]: " << value;

    // Show type information
    ss << " (size: " << sizeof(value) << " bytes)";

    // Bit representation for deeper debugging
    union {
        double d;
        uint64_t i;
    } u;
    u.d = value;

    ss << " hex: 0x" << std::hex << u.i;

    safePrint(ss.str(), true);
}

// Debugging function for Optical Flow Matrix with additional details
inline void debugFlowMatrix(const cv::Mat& flow, const std::string& tag, bool verbose) {
    if (!verbose) return;

    if (flow.empty()) {
        safePrint(tag + ": Flow matrix is empty", true);
        return;
    }

    // Extract the X-component of the flow
    std::vector<cv::Mat> flowComponents;
    cv::split(flow, flowComponents);

    if (flowComponents.empty()) {
        safePrint(tag + ": No flow components extracted", true);
        return;
    }

    // Statistics for the X-component
    cv::Scalar mean, stddev;
    cv::meanStdDev(flowComponents[0], mean, stddev);

    // Find min/max values
    double minVal, maxVal;
    cv::minMaxLoc(flowComponents[0], &minVal, &maxVal);

    std::stringstream ss;
    ss << std::fixed << std::setprecision(12); // Increased precision for debug output
    ss << tag << " Flow X-component: "
       << "mean=" << mean[0]
       << ", stddev=" << stddev[0]
       << ", min=" << minVal
       << ", max=" << maxVal;

    // Collect some sample values from the matrix with high precision
    ss << ", samples=[";
    int numSamples = std::min(5, flow.rows);
    for (int i = 0; i < numSamples; i++) {
        int row = flow.rows / (numSamples + 1) * (i + 1);
        int col = flow.cols / 2; // Middle column
        if (row < flowComponents[0].rows && col < flowComponents[0].cols) {
            float xValue = flowComponents[0].at<float>(row, col);
            ss << std::setprecision(12) << xValue;
            if (i < numSamples - 1) ss << ", ";
        }
    }
    ss << "]";

    safePrint(ss.str(), true);
}

#endif // DEBUG_UTILS_H